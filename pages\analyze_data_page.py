
"""
Analyze Data Page for the WOSS Seismic Analysis Tool.

This module handles the UI rendering for analyzing the data.
It follows the principles outlined in rules.md, particularly regarding
the separation of concerns between UI and backend logic.
"""

import streamlit as st
import numpy as np
import pandas as pd
import logging
import os
import tempfile
import segyio
import zipfile
import shutil
from io import BytesIO
from tqdm import tqdm  # Import tqdm for progress bars

# Import common modules
from common.constants import (
    APP_TITLE, AVAILABLE_OUTPUTS_SINGLE, AVAILABLE_OUTPUTS_MULTI,
    AVAILABLE_OUTPUTS_SECTION
)
from common.session_state import initialize_session_state, reset_state
from common.ui_elements import get_suggested_batch_size

# Import utility functions
from utils.data_utils import get_well_marker_pairs, get_nearest_trace_index, load_trace_sample # Corrected import
from utils.general_utils import find_traces_near_polyline, parse_polyline_string
from utils.processing import calculate_woss
from utils.visualization import plot_trace_with_descriptors, plot_descriptor_section # Visualization imports

# Helper functions for AOI export
def get_suggested_batch_size_for_export(num_unique_groups):
    """
    Suggest a reasonable batch size for export based on the number of unique groups.

    Args:
        num_unique_groups: Number of unique groups (inlines, crosslines, etc.)

    Returns:
        int: Suggested batch size for export
    """
    # Simple heuristic: for small numbers, use the number itself
    if num_unique_groups <= 10:
        return num_unique_groups

    # For larger numbers, use a fraction to keep file sizes manageable
    elif num_unique_groups <= 100:
        return max(10, num_unique_groups // 5)
    elif num_unique_groups <= 1000:
        return max(20, num_unique_groups // 10)
    else:
        return max(50, num_unique_groups // 20)

# Import GPU utility functions
from utils.gpu_utils import check_gpu_availability, log_gpu_info

# Import GPU functions if available
try:
    from utils.dlogst_spec_descriptor_gpu import (
        dlogst_spec_descriptor_gpu,
        dlogst_spec_descriptor_gpu_2d_chunked,
        dlogst_spec_descriptor_gpu_2d_chunked_mag
    )
    # Check if GPU is actually available and working
    GPU_AVAILABLE = check_gpu_availability()
    if GPU_AVAILABLE:
        logging.info("Successfully imported GPU spectral descriptor functions and verified GPU is working")
        # Log GPU information for debugging
        log_gpu_info()
    else:
        logging.warning("GPU functions imported but GPU is not available or not working correctly")
except ImportError as e:
    logging.warning(f"Could not import GPU functions: {e}")
    GPU_AVAILABLE = False
    # Define dummy functions
    def dlogst_spec_descriptor_gpu(*args, **kwargs):
        st.error("GPU function dlogst_spec_descriptor_gpu not available.")
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu not available.")
    def dlogst_spec_descriptor_gpu_2d_chunked(*args, **kwargs):
        st.error("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")
    def dlogst_spec_descriptor_gpu_2d_chunked_mag(*args, **kwargs):
        st.error("GPU function dlogst_spec_descriptor_gpu_2d_chunked_mag not available.")
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked_mag not available.")
        

# Map of display attribute names to internal names
ATTR_NAME_MAP = {
    "Original Seismic Amplitude": "data",
    "Magnitude of the Voice Component": "voice_mag",
    "High-Frequency Content (HFC)": "hfc",
    "Normalized Dominant Frequency": "norm_fdom",
    "Magnitude of Voice Slope": "mag_voice_slope",
    "Weighted Optimum Spectral Shape": "WOSS"
}

# Reverse mapping for internal to display names
REVERSE_ATTR_NAME_MAP = {v: k for k, v in ATTR_NAME_MAP.items()}

def render_aoi_export():
    """Handle the AOI export configuration, processing, and file generation."""
    st.header("Step 4: AOI Export Configuration")
    
    # Ensure we have the necessary data
    if not st.session_state.get('selected_indices') or not st.session_state.get('header_loader'):
        st.warning("No AOI selection found. Please select an Area of Interest in Step 3.")
        if st.button("Return to Step 3"):
            st.session_state.current_step = "select_mode"
            st.rerun()
        return
    
    # Check if export is already in progress
    if st.session_state.get('export_in_progress', False):
        render_export_process()
        return
    
    # Check if we need to show download UI
    if st.session_state.get('export_complete', False):
        render_download_export()
        return
        
    # Show configuration UI
    st.subheader("Configure AOI Export")
    
    # Display AOI information
    aoi_bounds = st.session_state.get('aoi_bounds', {})
    if aoi_bounds:
        st.info(f"Selected AOI: Inlines {aoi_bounds['inline_min']} to {aoi_bounds['inline_max']}, "
                f"Crosslines {aoi_bounds['xline_min']} to {aoi_bounds['xline_max']}")
    
    # Display number of traces
    st.success(f"Found {len(st.session_state.selected_indices)} traces within the AOI.")
    
    # Attribute selection
    st.subheader("Export Configuration")
    
    # Define options for export
    available_for_export_display = [
        "Original Seismic Amplitude",
        "Magnitude of the Voice Component",
        "High-Frequency Content (HFC)",
        "Normalized Dominant Frequency",
        "Magnitude of Voice Slope",
        "WOSS"
    ]
    
    # Select attributes to export
    selected_attrs_display = st.multiselect(
        "Select Attributes to Export:",
        options=available_for_export_display,
        default=[attr_name for attr_name in available_for_export_display if attr_name != "Original Seismic Amplitude"] # Default to all except original
    )
    selected_attrs_internal = [ATTR_NAME_MAP[attr_name] for attr_name in selected_attrs_display]
    
    # Grouping and Batching
    grouping_type = st.selectbox(
        "Group Export Files By:",
        options=["inline", "crossline"],
        index=0,
        key="export_grouping_select"
    )
    st.session_state.export_grouping = grouping_type # Store selection
    
    # Get unique group values for the selected grouping
    headers_df = pd.DataFrame({
        'inline': st.session_state.header_loader.inlines,
        'crossline': st.session_state.header_loader.crosslines,
        'trace_idx': st.session_state.header_loader.unique_indices
    })
    
    # Filter to only selected traces
    selected_headers = headers_df[headers_df['trace_idx'].isin(st.session_state.selected_indices)]
    unique_group_values = selected_headers[grouping_type].unique()
    num_unique_groups = len(unique_group_values)
    
    # Suggest batch size based on grouping
    suggested_batch = get_suggested_batch_size_for_export(num_unique_groups)
    st.info(f"Found {num_unique_groups} unique {grouping_type} values in the AOI.")
    
    # Batch step selection
    batch_step = st.number_input(
        f"Number of {grouping_type}s per batch file (suggested: {suggested_batch}):",
        min_value=1,
        max_value=max(100, num_unique_groups),
        value=suggested_batch,
        step=1,
        key="export_batch_step"
    )
    st.session_state.export_batch_step = batch_step
    
    # GPU batch size for processing
    if GPU_AVAILABLE:
        st.subheader("GPU Processing Configuration")
        gpu_batch_size = st.number_input(
            "GPU Processing Batch Size (traces per chunk):",
            min_value=1,
            max_value=1000,
            value=get_suggested_batch_size(len(st.session_state.selected_indices)),
            step=1,
            help="Number of traces to process in each GPU batch. Higher values use more memory but may be faster.",
            key="gpu_batch_size_input"
        )
        st.session_state.batch_size = gpu_batch_size
    else:
        st.warning("GPU processing is not available. AOI export requires GPU acceleration.")
    
    # Disable export button if no attributes or no GPU
    disable_export_button = not GPU_AVAILABLE or not selected_attrs_internal
    
    # Start export button
    if st.button("Start Export Process", key="start_export_button", disabled=disable_export_button):
        if not selected_attrs_internal:
            st.error("Please select at least one attribute to export.")
        elif not GPU_AVAILABLE:
            st.error("GPU is required for AOI export processing and is not available.")
        else:
            # Store selected attributes for export
            st.session_state.export_attributes = selected_attrs_internal
            
            # Create output directory
            if not st.session_state.get('export_output_dir'):
                st.session_state.export_output_dir = tempfile.mkdtemp(prefix="woss_aoi_export_")
                logging.info(f"Created export output directory: {st.session_state.export_output_dir}")
            
            # Set export in progress flag
            st.session_state.export_in_progress = True
            st.session_state.export_progress = 0
            st.session_state.export_current_batch = 0
            st.session_state.export_total_batches = 0
            
            # Create tracking variables for progress
            unique_values = sorted(unique_group_values)  # Sort the unique values
            num_batches = (len(unique_values) + batch_step - 1) // batch_step  # Ceiling division
            
            # Store export configuration
            st.session_state.export_unique_values = unique_values
            st.session_state.export_total_batches = num_batches
            
            st.rerun()


def render_export_process():
    """Handle the actual export processing and generation of SEG-Y files."""
    st.header("Step 4.5: Processing AOI Export")
    
    # Check if export was actually started
    if not st.session_state.get('export_in_progress', False):
        st.warning("Export process not initiated correctly. Please configure export first.")
        if st.button("Go to Export Configuration"):
            st.session_state.export_in_progress = False
            st.rerun()
        return
    
    # Show progress information
    st.subheader("Export Progress")
    
    # Display batch progress
    if st.session_state.export_total_batches > 0:
        progress_text = f"Batch {st.session_state.export_current_batch + 1} of {st.session_state.export_total_batches}"
        progress_bar = st.progress(st.session_state.export_progress)
        st.text(progress_text)
    
    # Process current batch if not complete
    if st.session_state.export_current_batch < st.session_state.export_total_batches:
        # Get batch information
        batch_size = st.session_state.export_batch_step
        current_batch = st.session_state.export_current_batch
        unique_values = st.session_state.export_unique_values
        grouping_type = st.session_state.export_grouping
        
        # Calculate batch range
        start_idx = current_batch * batch_size
        end_idx = min(start_idx + batch_size, len(unique_values))
        batch_values = unique_values[start_idx:end_idx]
        
        # Display batch information
        if grouping_type == "inline":
            st.info(f"Processing inlines {batch_values[0]} to {batch_values[-1]}")
        else:
            st.info(f"Processing crosslines {batch_values[0]} to {batch_values[-1]}")
        
        # Get traces for this batch
        headers_df = pd.DataFrame({
            'inline': st.session_state.header_loader.inlines,
            'crossline': st.session_state.header_loader.crosslines,
            'trace_idx': st.session_state.header_loader.unique_indices
        })
        
        # Filter to traces in selected AOI and current batch
        selected_headers = headers_df[headers_df['trace_idx'].isin(st.session_state.selected_indices)]
        batch_headers = selected_headers[selected_headers[grouping_type].isin(batch_values)]
        batch_trace_indices = batch_headers['trace_idx'].tolist()
        
        # Check if we have traces to process
        if not batch_trace_indices:
            st.warning(f"No traces found for this batch of {grouping_type} values. Skipping.")
            # Move to next batch
            st.session_state.export_current_batch += 1
            st.session_state.export_progress = st.session_state.export_current_batch / st.session_state.export_total_batches
            st.rerun()
            return
        
        # Load trace data for the batch
        with st.spinner(f"Loading {len(batch_trace_indices)} traces for processing..."):
            try:
                section_data_2d = np.zeros((len(batch_trace_indices), len(st.session_state.header_loader.segyfile.samples)))
                for i, idx in enumerate(batch_trace_indices):
                    trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, idx)
                    if trace_sample is not None:
                        section_data_2d[i] = trace_sample
            except Exception as e:
                st.error(f"Error loading trace data: {e}")
                logging.error(f"Error loading trace data: {e}", exc_info=True)
                return
        
        # Process the data using GPU functions
        with st.spinner("Calculating attributes using GPU..."):
            try:
                # Set up parameters from session state
                spectral_params = st.session_state.plot_settings.copy()
                
                # Get list of attributes to calculate
                export_attributes = st.session_state.export_attributes
                
                # Add required components for WOSS if it's selected
                if "WOSS" in export_attributes and not all(attr in export_attributes for attr in ["hfc", "norm_fdom", "mag_voice_slope"]):
                    # Ensure all needed components are included
                    for attr in ["hfc", "norm_fdom", "mag_voice_slope"]:
                        if attr not in export_attributes:
                            export_attributes.append(attr)
                
                # Calculate the descriptors
                all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked(
                    section_data_2d,
                    st.session_state.dt,
                    batch_size=st.session_state.batch_size,
                    **spectral_params
                )

                # Filter to only the requested descriptors
                calculated_attribute_sections = {key: all_descriptors[key] for key in export_attributes
                                                if key in all_descriptors}
                
                # Add original seismic data if selected
                if "data" in export_attributes:
                    calculated_attribute_sections["data"] = section_data_2d
                
                # Calculate WOSS if needed
                if "WOSS" in export_attributes:
                    # Get HFC p95 from session state or use default
                    hfc_p95 = st.session_state.get('hfc_p95', 1.0)
                    
                    # Create WOSS parameters
                    woss_params = {
                        'hfc_p95': hfc_p95,
                        'epsilon': spectral_params.get('epsilon', 1e-4),
                        'fdom_exponent': spectral_params.get('fdom_exponent', 2.0)
                    }
                    
                    # Calculate WOSS for each trace
                    woss_array = np.zeros_like(calculated_attribute_sections['hfc'])
                    for i in range(section_data_2d.shape[0]):
                        trace_components = {
                            'hfc': calculated_attribute_sections['hfc'][i],
                            'norm_fdom': calculated_attribute_sections['norm_fdom'][i],
                            'mag_voice_slope': calculated_attribute_sections['mag_voice_slope'][i]
                        }
                        woss_array[i] = calculate_woss(trace_components, woss_params)
                    
                    # Add to calculated attributes
                    calculated_attribute_sections["WOSS"] = woss_array
            except Exception as e:
                st.error(f"Error calculating attributes: {e}")
                logging.error(f"Error calculating attributes: {e}", exc_info=True)
                return
        
        # Create SEG-Y files for each attribute
        with st.spinner("Creating SEG-Y files..."):
            try:
                # Filter to only the requested export attributes (not the components required for calculation)
                export_attributes_final = [attr for attr in export_attributes if attr in st.session_state.export_attributes]
                
                # Create a file for each attribute
                created_files = []
                for attr_name in export_attributes_final:
                    # Skip if data wasn't calculated for some reason
                    if attr_name not in calculated_attribute_sections:
                        logging.warning(f"Attribute {attr_name} was not calculated. Skipping export.")
                        continue
                    
                    # Create descriptive filename
                    display_name = REVERSE_ATTR_NAME_MAP.get(attr_name, attr_name).replace(" ", "_")
                    if grouping_type == "inline":
                        filename = f"{display_name}_Inlines_{batch_values[0]}-{batch_values[-1]}.sgy"
                    else:
                        filename = f"{display_name}_Crosslines_{batch_values[0]}-{batch_values[-1]}.sgy"
                    
                    output_path = os.path.join(st.session_state.export_output_dir, filename)
                    
                    # Get the attribute data
                    attribute_data = calculated_attribute_sections[attr_name]
                    
                    # Create a spec for the output file
                    spec = segyio.tools.metadata(st.session_state.header_loader.segyfile)
                    spec.samples = st.session_state.header_loader.segyfile.samples
                    spec.tracecount = len(batch_trace_indices)
                    
                    # Create the SEG-Y file
                    with segyio.create(output_path, spec) as dst:
                        # Copy binary header
                        dst.bin = st.session_state.header_loader.segyfile.bin
                        
                        # Copy text header if available
                        try:
                            dst.text[0] = st.session_state.header_loader.segyfile.text[0]
                        except:
                            pass
                        
                        # Write each trace with its header
                        for i, trace_idx in enumerate(batch_trace_indices):
                            # Get the original trace header
                            src_trace_idx = np.where(st.session_state.header_loader.unique_indices == trace_idx)[0][0]
                            dst.header[i] = st.session_state.header_loader.segyfile.header[src_trace_idx]
                            
                            # Write the calculated attribute data as the trace
                            dst.trace[i] = attribute_data[i]
                    
                    created_files.append(output_path)
                    logging.info(f"Created SEG-Y file: {output_path}")
                
                # Store information about created files
                if not hasattr(st.session_state, 'created_files_list'):
                    st.session_state.created_files_list = []
                st.session_state.created_files_list.extend(created_files)
                
            except Exception as e:
                st.error(f"Error creating SEG-Y files: {e}")
                logging.error(f"Error creating SEG-Y files: {e}", exc_info=True)
                return
        
        # Move to next batch
        st.session_state.export_current_batch += 1
        st.session_state.export_progress = st.session_state.export_current_batch / st.session_state.export_total_batches
        
        # Check if we're done
        if st.session_state.export_current_batch >= st.session_state.export_total_batches:
            st.session_state.export_in_progress = False
            st.session_state.export_complete = True
            st.success("Export processing complete! Ready for download.")
        
        # Rerun to process next batch or show download UI
        st.rerun()
    
    # Show cancel button
    if st.button("Cancel Export", key="cancel_export_button"):
        # Clean up temporary directory
        if st.session_state.get('export_output_dir') and os.path.exists(st.session_state.export_output_dir):
            try:
                shutil.rmtree(st.session_state.export_output_dir)
                logging.info(f"Removed export directory: {st.session_state.export_output_dir}")
            except Exception as e:
                logging.error(f"Error removing export directory: {e}")
        
        # Reset export state
        st.session_state.export_in_progress = False
        st.session_state.export_complete = False
        if hasattr(st.session_state, 'created_files_list'):
            del st.session_state.created_files_list
        
        st.success("Export cancelled.")
        st.rerun()


def render_download_export():
    """Provide a download interface for the exported files."""
    st.header("Step 4.6: Download Export Files")
    
    # Check if we have files to download
    if not hasattr(st.session_state, 'created_files_list') or not st.session_state.created_files_list:
        st.warning("No export files found. Please run the export process first.")
        if st.button("Return to Export Configuration"):
            st.session_state.export_complete = False
            st.rerun()
        return
    
    # Show success message
    st.success(f"Successfully created {len(st.session_state.created_files_list)} SEG-Y files!")
    
    # Create a zip file of all exported files
    try:
        zip_buffer = BytesIO()
        # Use a base filename derived from the original SEG-Y if possible
        base_zip_filename = "WOSS_AOI_Export_Files.zip"
        if 'segy_file_info' in st.session_state and st.session_state.segy_file_info:
            base_zip_filename = f"{st.session_state.segy_file_info['name']}_AOI_Export_Files.zip"
        
        # Create the zip file
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for file_path in st.session_state.created_files_list:
                if os.path.exists(file_path):
                    # Add file to zip with just the filename (not the full path)
                    zip_file.write(file_path, os.path.basename(file_path))
                else:
                    logging.warning(f"File not found for zip: {file_path}")
        
        # Reset the buffer position to the beginning
        zip_buffer.seek(0)
        
        # Provide download button
        st.download_button(
            label="📥 Download All Files as ZIP",
            data=zip_buffer,
            file_name=base_zip_filename,
            mime="application/zip",
            key="download_zip_button"
        )
        
        # Show information about the exported files
        st.subheader("Exported Files")
        for i, file_path in enumerate(st.session_state.created_files_list):
            st.text(f"{i+1}. {os.path.basename(file_path)}")
        
    except Exception as e:
        st.error(f"Error creating zip file: {e}")
        logging.error(f"Error creating zip file: {e}", exc_info=True)
    
    # Add buttons for further actions
    col1, col2 = st.columns(2)
    with col1:
        if st.button("Start New Export", key="new_export_button"):
            # Clean up directory but keep loaded data
            if st.session_state.get('export_output_dir') and os.path.exists(st.session_state.export_output_dir):
                try:
                    shutil.rmtree(st.session_state.export_output_dir)
                    logging.info(f"Removed export directory: {st.session_state.export_output_dir}")
                except Exception as e:
                    logging.error(f"Error removing export directory: {e}")
            
            # Reset export state but keep selection mode
            st.session_state.export_in_progress = False
            st.session_state.export_complete = False
            if hasattr(st.session_state, 'created_files_list'):
                del st.session_state.created_files_list
            if hasattr(st.session_state, 'export_output_dir'):
                del st.session_state.export_output_dir
            
            st.rerun()
    
    with col2:
        if st.button("Start New Analysis", key="start_new_analysis_button"):
            # Full reset of the application state
            reset_state()
            st.success("Starting new analysis. All temporary data has been cleared.")
            st.rerun()


def render():
    """Render the analyze data page UI."""
    # Initialize session state if needed
    initialize_session_state()

    st.header("Step 4: Analyze Data")
    st.sidebar.header("Analysis Options")

    # Check if data is loaded
    if not st.session_state.header_loader:
        st.warning("Please load data first.")
        st.session_state.current_step = "load_data"
        st.rerun()
        return

    # Initialize precomputed_data as None
    precomputed_data = None

    # Check if we have precomputed data (from previous implementation)
    if st.session_state.get('precomputed_data_output') is not None:
        precomputed_data = st.session_state.get('precomputed_data_output')
        logging.info("Using existing precomputed data")
    else:
        # If no precomputed data, we'll work with raw data directly
        logging.info("No precomputed data found, will work with raw data directly")

    # Check if we have the HFC p95 value for WOSS calculation
    hfc_p95_value = st.session_state.get('hfc_p95')
    if hfc_p95_value is None:
        st.warning("HFC p95 value not found in session state. WOSS calculation might be inaccurate.")
        # We'll continue anyway, as calculate_woss has a fallback mechanism
    else:
        st.info(f"Using HFC p95 value: {hfc_p95_value:.3f} for WOSS calculation.")

    # Store GPU availability in session state
    st.session_state.GPU_AVAILABLE = GPU_AVAILABLE
    
    # Handle AOI mode specially - use separate export flow
    if st.session_state.get('selection_mode') == "By inline/crossline section (AOI)":
        render_aoi_export()
        return

    # Well Markers Mode
    if st.session_state.selection_mode == "By well markers":
        st.subheader("Selected Area Information") # Add subheader for clarity
        if st.session_state.well_df is not None and not st.session_state.well_df.empty:
            # Display information about the selected area from session state
            if st.session_state.get('selected_well_markers'):
                 st.write(f"Selected well markers: {', '.join(st.session_state.selected_well_markers)}")
            if st.session_state.get('selected_well_marker_pairs'):
                 st.write(f"Selected well-marker pairs: {', '.join(['{well} - {surface}'.format(well=pair.get('Well', 'Unknown'), surface=pair.get('Surface', 'Unknown')) for pair in st.session_state.selected_well_marker_pairs])}") # Display selected pairs
            if st.session_state.get('selected_indices'):
                 st.write(f"Number of selected traces: {len(st.session_state.selected_indices)}")

            # Use the loaded trace data from select_area_page.py
            loaded_trace_data = st.session_state.get('loaded_trace_data', [])

            if loaded_trace_data:
                st.success(f"Loaded {len(loaded_trace_data)} traces for analysis.")
            else:
                st.warning("No traces loaded for analysis from the selected well-marker pairs.")

            # Add a button to calculate descriptors
            st.markdown("---")
            st.markdown("### Next Steps:")
            st.markdown("1. Click **Calculate Descriptors** to process the selected traces and compute signal descriptors")
            st.markdown("2. Review the statistical summary that will appear")
            st.markdown("3. Click **View Results** to proceed to the results page for visualization and analysis options")

            if not st.session_state.get('GPU_AVAILABLE', False):
                st.error("GPU processing is required for this analysis mode, but no GPU is available. Please check your system configuration.")
            elif st.button("Calculate Descriptors", key="calculate_descriptors_button", help="Process the selected traces and compute spectral descriptors"):
                if not loaded_trace_data:
                    st.warning("No traces loaded to calculate descriptors.")
                    return

                # Get the selected analysis sub-option
                analysis_sub_option = st.session_state.get("well_analysis_sub_option", "Grouping Well Analysis") # Default to grouping if not set

                if analysis_sub_option == "Plot Individual Wells Analysis":
                    st.session_state.individual_well_analysis_results = [] # Initialize
                    # Placeholder for individual well analysis logic
                    # This will iterate through loaded_trace_data, calculate descriptors and stats for each
                    # and append to st.session_state.individual_well_analysis_results
                    # For now, just set analysis_complete to True to allow UI to proceed
                    
                    # --- Start of Detailed Individual Well Analysis Logic ---
                    num_traces = len(loaded_trace_data)
                    skipped_traces_count = 0
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    status_text.text("Initializing individual well descriptor calculation...")

                    # Ensure plot_settings time/frequency limits are set once before the loop if they affect all traces
                    # This logic is similar to backup/app_ref.py:1543-1555
                    if 'Time (Y-axis)' not in st.session_state.plot_settings and 'time_min' in st.session_state.plot_settings:
                        st.session_state.plot_settings['Time (Y-axis)'] = (
                            st.session_state.plot_settings.get('time_min', 0.0),
                            st.session_state.plot_settings.get('time_max', 4.0) # Assuming dt is 4ms, so 4s is 1000 samples
                        )
                    if 'Frequency' not in st.session_state.plot_settings and 'freq_min' in st.session_state.plot_settings:
                        nyquist_freq = 0.5 / st.session_state.dt if hasattr(st.session_state, 'dt') and st.session_state.dt > 0 else 125.0
                        st.session_state.plot_settings['Frequency'] = (
                            st.session_state.plot_settings.get('freq_min', 0.0),
                            st.session_state.plot_settings.get('freq_max', nyquist_freq)
                        )

                    for i, trace_data in enumerate(loaded_trace_data):
                        well_marker_name = trace_data.get('well_marker_name', 'Unknown')
                        status_text.text(f"Processing trace {i+1}/{num_traces} for {well_marker_name} (Individual)...")
                        
                        descriptor = {}
                        individual_trace_stats = {}

                        try:
                            # 1. Descriptor Calculation (mirroring backup/app_ref.py:1530-1579)
                            base_plot_settings = st.session_state.get('plot_settings', {})
                            descriptor_settings = {
                                'use_band_limited': base_plot_settings.get('use_band_limited', False),
                                'shape': base_plot_settings.get('shape', 0.35), # Current app uses float, ref used string. Align if necessary.
                                'kmax': base_plot_settings.get('kmax', 120.0),
                                'int_val': base_plot_settings.get('int_val', 35.0),
                                'b1': base_plot_settings.get('b1', 5.0),
                                'b2': base_plot_settings.get('b2', 40.0),
                                'p_bandwidth': base_plot_settings.get('p_bandwidth', 2.0),
                                'roll_percent': base_plot_settings.get('roll_percent', 0.80),
                                # Epsilon is primarily for WOSS, not directly for dlogst_spec_descriptor_gpu
                            }
                            
                            gpu_descriptor_settings = {
                                k: v for k, v in descriptor_settings.items()
                                # Filter out params not used by dlogst_spec_descriptor_gpu, e.g., 'epsilon' if it were here
                                # Based on backup/app_ref.py, 'epsilon', 'fdom_exponent', 'hfc_p95' are filtered.
                                # Current descriptor_settings doesn't have them, so filter is less critical but good practice.
                                if k not in ['epsilon', 'fdom_exponent', 'hfc_p95']
                            }

                            trace_sample = trace_data['trace_sample']
                            if trace_sample is None or len(trace_sample) == 0:
                                raise ValueError("Invalid trace sample: empty or None")

                            # Validate trace sample data
                            if not isinstance(trace_sample, np.ndarray):
                                trace_sample = np.array(trace_sample, dtype=np.float32)

                            if np.all(np.isnan(trace_sample)):
                                raise ValueError("Trace sample contains only NaN values")

                            if np.all(trace_sample == 0):
                                logging.warning(f"Trace sample for {well_marker_name} contains only zeros")

                            fmax_value = len(trace_sample) // 2 # As in current analyze_data_page.py

                            # Log detailed information for debugging
                            logging.info(f"Processing {well_marker_name}: trace_length={len(trace_sample)}, fmax={fmax_value}, dt={st.session_state.dt}")
                            logging.info(f"GPU settings: {gpu_descriptor_settings}")

                            descriptor = dlogst_spec_descriptor_gpu(
                                trace_sample,
                                st.session_state.dt,
                                fmax=fmax_value, # Keep fmax based on current implementation
                                **gpu_descriptor_settings
                            )

                            if descriptor is None:
                                raise ValueError("GPU function returned None instead of descriptor dictionary.")
                            if not isinstance(descriptor, dict):
                                raise ValueError(f"GPU function returned {type(descriptor)} instead of dictionary.")
                            if 'data' not in descriptor:
                                available_keys = list(descriptor.keys()) if isinstance(descriptor, dict) else "N/A"
                                raise ValueError(f"GPU descriptor missing 'data' key. Available keys: {available_keys}")

                            # Log successful calculation
                            logging.info(f"Successfully calculated descriptors for {well_marker_name}. Keys: {list(descriptor.keys())}")

                            # 2. Statistics Calculation (mirroring backup/app_ref.py:1585-1779 for this single descriptor)
                            # Define descriptor_mapping for this analysis
                            descriptor_mapping = {
                                # Signal data
                                "Input Signal": "data",

                                # Spectrograms and time-frequency representations
                                "Magnitude Spectrogram": "tf_map",
                                "Magnitude * Voice": "mag_voice",

                                # Frequency-related descriptors
                                "Normalized dominant frequencies": "norm_fdom",
                                "Normalized Dominant Frequency": "norm_fdom",
                                "Dominant Frequency": "fdom",
                                "Peak Frequency": "peak_freq",
                                "Spectral Centroid": "spec_centroid",

                                # Slope-related descriptors
                                "Spectral Slope": "spec_slope",
                                "Mag*Voice Slope": "mag_voice_slope",
                                "Voice Slope": "voice_slope",
                                "Slope Magnitude": "mag_voice_slope",

                                # Bandwidth and spectral shape descriptors
                                "Spectral Bandwidth": "spec_bandwidth",
                                "Spectral Rolloff": "spec_rolloff",
                                "Spectral Decrease": "spec_decrease",
                                "Normalized Spectral Decrease": "spec_decrease",

                                # High-frequency content
                                "HFC": "hfc",
                                "Normalized HFC": "hfc",  # We'll normalize this manually

                                # Composite descriptors
                                "WOSS": "WOSS"
                            }
                            
                            # Initialize stats for this specific trace
                            current_trace_stats_values = {}
                            for output_key in AVAILABLE_OUTPUTS_SINGLE + AVAILABLE_OUTPUTS_MULTI: # Use combined list as per reference
                                current_trace_stats_values[output_key] = {'values': []}
                            current_trace_stats_values['Normalized HFC'] = {'values': []} # Custom stat
                            current_trace_stats_values['Normalized Spectral Decrease'] = {'values': []}
                            # Add specific keys for spectrogram stats if they are calculated and stored per trace
                            for spec_stat_base in ["Magnitude Spectrogram", "Magnitude * Voice"]:
                                for stat_suffix in ["_max", "_median", "_std"]:
                                    current_trace_stats_values[spec_stat_base + stat_suffix] = {'values': []}


                            # Process the single descriptor for this trace
                            if descriptor and not descriptor.get('error'):
                                for output_name, values_dict in current_trace_stats_values.items():
                                    internal_key = descriptor_mapping.get(output_name)
                                    
                                    # Handle spectrogram aggregate stats names correctly
                                    is_spec_agg_stat = False
                                    for spec_base in ["Magnitude Spectrogram", "Magnitude * Voice"]:
                                        if output_name.startswith(spec_base + "_"):
                                            internal_key = descriptor_mapping.get(spec_base) # Base key for data
                                            is_spec_agg_stat = True
                                            break
                                    
                                    if not internal_key or internal_key not in descriptor:
                                        continue

                                    desc_value = descriptor[internal_key]

                                    if output_name == "Magnitude Spectrogram" and isinstance(desc_value, np.ndarray) and desc_value.ndim == 2:
                                        mean_val = np.mean(desc_value)
                                        if not np.isnan(mean_val): values_dict['values'].append(mean_val)
                                        if not np.isnan(np.max(desc_value)): current_trace_stats_values["Magnitude Spectrogram_max"]['values'].append(np.max(desc_value))
                                        if not np.isnan(np.median(desc_value)): current_trace_stats_values["Magnitude Spectrogram_median"]['values'].append(np.median(desc_value))
                                        if not np.isnan(np.std(desc_value)): current_trace_stats_values["Magnitude Spectrogram_std"]['values'].append(np.std(desc_value))
                                    elif output_name == "Magnitude * Voice" and isinstance(desc_value, np.ndarray) and desc_value.ndim == 2:
                                        mean_val = np.mean(desc_value)
                                        if not np.isnan(mean_val): values_dict['values'].append(mean_val)
                                        if not np.isnan(np.max(desc_value)): current_trace_stats_values["Magnitude * Voice_max"]['values'].append(np.max(desc_value))
                                        if not np.isnan(np.median(desc_value)): current_trace_stats_values["Magnitude * Voice_median"]['values'].append(np.median(desc_value))
                                        if not np.isnan(np.std(desc_value)): current_trace_stats_values["Magnitude * Voice_std"]['values'].append(np.std(desc_value))
                                    elif output_name == 'Normalized HFC' and 'hfc' in descriptor:
                                        hfc_p95_setting = base_plot_settings.get('hfc_p95', 1.0) # Get from plot_settings
                                        if hfc_p95_setting > 0:
                                            normalized_hfc_val = descriptor['hfc'] / hfc_p95_setting
                                            values_dict['values'].extend(normalized_hfc_val if isinstance(normalized_hfc_val, np.ndarray) else [normalized_hfc_val])
                                    elif output_name == 'Normalized Spectral Decrease' and 'spec_decrease' in descriptor:
                                        spec_decrease_p95_setting = base_plot_settings.get('spec_decrease_p95', 1.0)
                                        if spec_decrease_p95_setting > 0:
                                            normalized_spec_decrease_val = descriptor['spec_decrease'] / spec_decrease_p95_setting
                                            values_dict['values'].extend(normalized_spec_decrease_val if isinstance(normalized_spec_decrease_val, np.ndarray) else [normalized_spec_decrease_val])
                                    elif output_name == 'WOSS' and 'WOSS' not in descriptor and all(k in descriptor for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                        woss_calc_params = base_plot_settings.copy() # Start with plot_settings
                                        # Ensure hfc_p95 is in woss_calc_params, sourced from session_state.hfc_p95 or plot_settings.hfc_p95
                                        if st.session_state.get('hfc_p95') is not None:
                                            woss_calc_params['hfc_p95'] = st.session_state.hfc_p95
                                        elif 'hfc_p95' not in woss_calc_params and 'hfc' in descriptor: # Fallback if not globally set
                                             woss_calc_params['hfc_p95'] = np.percentile(descriptor['hfc'], base_plot_settings.get('hfc_percentile', 95.0))

                                        woss_values = calculate_woss(descriptor, woss_calc_params) # calculate_woss is imported
                                        values_dict['values'].extend(woss_values if isinstance(woss_values, np.ndarray) else [woss_values])
                                    elif isinstance(desc_value, np.ndarray) and desc_value.ndim == 1 and not is_spec_agg_stat:
                                        values_dict['values'].extend(desc_value)
                                    elif not isinstance(desc_value, np.ndarray) and not is_spec_agg_stat: # Handle scalar descriptors
                                         values_dict['values'].append(desc_value)


                            # Calculate final statistics (min, max, p5, p90) for this trace
                            individual_trace_stats_summary = {}
                            for desc_name_stat, stat_vals_dict in current_trace_stats_values.items():
                                if stat_vals_dict['values']:
                                    vals = np.array(stat_vals_dict['values'])
                                    individual_trace_stats_summary[desc_name_stat] = {
                                        'min': np.min(vals), 'max': np.max(vals),
                                        'p5': np.percentile(vals, 5), 'p90': np.percentile(vals, 90),
                                        'mean': np.mean(vals), 'median': np.median(vals), 'std': np.std(vals) # Add more stats
                                    }
                                else:
                                    individual_trace_stats_summary[desc_name_stat] = {
                                        'min': 'N/A', 'max': 'N/A', 'p5': 'N/A', 'p90': 'N/A',
                                        'mean': 'N/A', 'median': 'N/A', 'std': 'N/A'
                                    }
                            individual_trace_stats = individual_trace_stats_summary


                            # 3. Store results
                            st.session_state.individual_well_analysis_results.append({
                                'well_marker_name': well_marker_name,
                                'trace_idx': trace_data.get('trace_idx'),
                                'descriptors': descriptor,
                                'statistics': individual_trace_stats
                            })
                            logging.info(f"Successfully processed and calculated stats for (Individual) {well_marker_name}")

                        except Exception as e:
                            logging.error(f"Individual well analysis failed for {well_marker_name}: {e}", exc_info=True)
                            st.session_state.individual_well_analysis_results.append({
                                'well_marker_name': well_marker_name,
                                'trace_idx': trace_data.get('trace_idx'),
                                'descriptors': {}, # Store empty if error during calc
                                'statistics': {},  # Store empty if error during calc
                                'error': str(e)
                            })
                            skipped_traces_count += 1
                        progress_bar.progress((i + 1) / num_traces)
                    
                    status_text.text("Individual well processing complete.")
                    progress_bar.empty()
                    if skipped_traces_count > 0:
                        st.warning(f"{skipped_traces_count} trace(s) failed during individual processing. Check logs.")
                    else:
                        st.success("All individual well analyses processed successfully.")
                    # --- End of Detailed Individual Well Analysis Logic ---

                    # Convert individual results to the format expected by view_results
                    # This ensures compatibility with the existing visualization code
                    converted_descriptors = []
                    for result in st.session_state.individual_well_analysis_results:
                        if 'descriptors' in result and result['descriptors']:
                            # Add metadata to the descriptor for identification
                            descriptor_copy = result['descriptors'].copy()
                            descriptor_copy['well_marker_name'] = result.get('well_marker_name', 'Unknown')
                            descriptor_copy['trace_idx'] = result.get('trace_idx')
                            converted_descriptors.append(descriptor_copy)
                        else:
                            # Add empty descriptor with error info for failed traces
                            converted_descriptors.append({
                                'error': result.get('error', 'Unknown error'),
                                'well_marker_name': result.get('well_marker_name', 'Unknown'),
                                'trace_idx': result.get('trace_idx')
                            })

                    st.session_state.calculated_descriptors = converted_descriptors
                    st.session_state.analysis_complete = True # Mark analysis as complete

                    # For individual analysis, we don't need global statistics
                    # but we should store some summary info for the UI
                    st.session_state.descriptor_statistics = {
                        'analysis_type': 'individual',
                        'total_traces': len(st.session_state.individual_well_analysis_results),
                        'successful_traces': len([r for r in st.session_state.individual_well_analysis_results if 'error' not in r]),
                        'failed_traces': len([r for r in st.session_state.individual_well_analysis_results if 'error' in r])
                    }


                elif analysis_sub_option == "Grouping Well Analysis":
                    # This is the existing logic for grouped analysis
                    calculated_descriptors = []
                    num_traces = len(loaded_trace_data)
                    skipped_traces_count = 0

                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    status_text.text("Initializing descriptor calculation...")

                    with st.spinner("Calculating spectral descriptors using GPU..."):
                        for i, trace_data in enumerate(loaded_trace_data):
                            well_marker_name = trace_data.get('well_marker_name', 'Unknown')
                            status_text.text(f"Processing trace {i+1}/{num_traces} for {well_marker_name} using GPU...")

                            descriptor = {}
                            try:
                                # Use the descriptor settings from plot_settings
                                descriptor_settings = {
                                    'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                                    'shape': st.session_state.plot_settings.get('shape', 0.35),
                                    'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                                    'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                                    'b1': st.session_state.plot_settings.get('b1', 5.0),
                                    'b2': st.session_state.plot_settings.get('b2', 40.0),
                                    'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                                    'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80),
                                    'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4) # Added epsilon
                                }

                                # Ensure time and frequency limits are properly set (can remain as is)
                                if 'Time (Y-axis)' not in st.session_state.plot_settings and 'time_min' in st.session_state.plot_settings:
                                    st.session_state.plot_settings['Time (Y-axis)'] = (
                                        st.session_state.plot_settings.get('time_min', 0.0),
                                        st.session_state.plot_settings.get('time_max', 4.0)
                                    )
                                if 'Frequency' not in st.session_state.plot_settings and 'freq_min' in st.session_state.plot_settings:
                                    nyquist_freq = 0.5 / st.session_state.dt if hasattr(st.session_state, 'dt') and st.session_state.dt > 0 else 125.0
                                    st.session_state.plot_settings['Frequency'] = (
                                        st.session_state.plot_settings.get('freq_min', 0.0),
                                        st.session_state.plot_settings.get('freq_max', nyquist_freq)
                                    )

                                trace_sample = trace_data['trace_sample']
                                fmax_value = len(trace_sample) // 2

                                logging.info(f"Attempting GPU processing for {well_marker_name}: length={len(trace_sample)}, fmax={fmax_value}")

                                # Prepare GPU-specific settings (excluding those not used by dlogst_spec_descriptor_gpu)
                                gpu_descriptor_settings = {
                                    k: v for k, v in descriptor_settings.items()
                                    if k not in ['epsilon', 'fdom_exponent', 'hfc_p95'] # Aligned with backup/app_ref.py
                                }

                                # Verify trace_sample is valid before GPU call
                                if trace_sample is None or len(trace_sample) == 0:
                                    raise ValueError(f"Invalid trace sample for {well_marker_name}: empty or None")

                                # Optional: NaN check before GPU, though dlogst_spec_descriptor_gpu should handle it
                                if np.isnan(trace_sample).any():
                                    logging.warning(f"Trace sample for {well_marker_name} contains NaN values. GPU function will handle or return error.")
                                    # trace_sample = np.nan_to_num(trace_sample, nan=0.0) # Decided to let GPU function handle

                                descriptor = dlogst_spec_descriptor_gpu(
                                    trace_sample,
                                    st.session_state.dt,
                                    fmax=fmax_value,
                                    **gpu_descriptor_settings
                                )

                                if descriptor is None:
                                    raise ValueError("GPU function returned None instead of descriptor dictionary.")

                                # Check if the descriptor itself indicates an error (e.g. from input validation within dlogst_spec_descriptor_gpu)
                                # This depends on how dlogst_spec_descriptor_gpu signals errors (e.g., specific keys or NaN content)
                                # For now, we assume if it returns, it's either valid or contains NaNs that stats will handle.
                                # A more robust check could be if critical fields are all NaN.
                                if 'data' not in descriptor or not isinstance(descriptor['data'], np.ndarray): # Basic check
                                    raise ValueError("GPU descriptor missing 'data' or invalid format.")


                                # Verify that critical descriptor keys are present (example)
                                # This check can be adjusted based on truly essential keys for subsequent steps.
                                expected_keys = ['hfc', 'norm_fdom', 'mag_voice_slope'] # Example critical keys
                                missing_critical_keys = [key for key in expected_keys if key not in descriptor or not isinstance(descriptor[key], np.ndarray) or descriptor[key].size == 0]

                                if missing_critical_keys:
                                    # Check if all values in critical keys are NaN, if the key exists
                                    all_nan_critical = True
                                    for key in expected_keys:
                                        if key in descriptor and isinstance(descriptor[key], np.ndarray) and descriptor[key].size > 0:
                                            if not np.all(np.isnan(descriptor[key])):
                                                all_nan_critical = False
                                                break
                                        else: # Key missing or not an array or empty
                                            pass # Already covered by missing_critical_keys

                                    if missing_critical_keys or all_nan_critical and not missing_critical_keys : # if keys are missing OR they exist but are all NaN
                                        # This indicates a failure in calculation for this trace.
                                        raise ValueError(f"Critical keys {missing_critical_keys if missing_critical_keys else expected_keys} missing or all NaN in GPU result for {well_marker_name}.")

                                logging.info(f"Successfully calculated GPU descriptors for {well_marker_name}")

                                if descriptor: # Ensure descriptor is not an empty dict from a caught error path
                                    descriptor['well_marker_name'] = well_marker_name
                                    descriptor['trace_idx'] = trace_data.get('trace_idx')
                                calculated_descriptors.append(descriptor)

                            except Exception as e:
                                logging.error(f"GPU descriptor calculation failed for trace {well_marker_name}: {e}", exc_info=True)
                                calculated_descriptors.append({'error': str(e), 'well_marker_name': well_marker_name, 'trace_idx': trace_data.get('trace_idx')})
                                st.warning(f"Could not process trace {well_marker_name} using GPU. Skipping. See logs for details.")
                                skipped_traces_count += 1

                        progress_bar.progress((i + 1) / num_traces)

                    status_text.text("All traces processed.")
                    progress_bar.empty()

                    st.session_state.calculated_descriptors = calculated_descriptors
                    st.session_state.analysis_complete = True

                    if skipped_traces_count > 0:
                        st.warning(f"Descriptor calculation finished. {skipped_traces_count} trace(s) could not be processed due to GPU errors. Please check logs for details.")
                    else:
                        st.success("Descriptor calculation complete.")

                    # Add a button to view results
                    st.markdown("---")
                    st.markdown("### Visualization:")
                    st.markdown("Click **View Results** to proceed to the results page for visualization and analysis options")

                    # This button is now outside the if/elif for sub-option,
                    # but will only be effective if st.session_state.analysis_complete is True
                    # The statistics calculation below is part of the "Grouping Well Analysis"
                    # For "Plot Individual Wells Analysis", statistics are per-well.
                    
                    # Calculate statistics for the descriptors (Only for Grouping Well Analysis)
                    if calculated_descriptors: # This will be populated by the Grouping Well Analysis path
                        # Initialize dictionaries to store statistics for all available outputs
                        stats = {}

                        # Create mapping between display names and internal descriptor keys
                        descriptor_mapping = {
                            # Signal data
                            "Input Signal": "data",

                            # Spectrograms and time-frequency representations
                            "Magnitude Spectrogram": "tf_map",
                            "Magnitude * Voice": "mag_voice",

                            # Frequency-related descriptors
                            "Normalized dominant frequencies": "norm_fdom",
                            "Normalized Dominant Frequency": "norm_fdom",
                            "Dominant Frequency": "fdom",
                            "Peak Frequency": "peak_freq",
                            "Spectral Centroid": "spec_centroid",

                            # Slope-related descriptors
                            "Spectral Slope": "spec_slope",
                            "Mag*Voice Slope": "mag_voice_slope",
                            "Voice Slope": "voice_slope",
                            "Slope Magnitude": "mag_voice_slope",

                            # Bandwidth and spectral shape descriptors
                            "Spectral Bandwidth": "spec_bandwidth",
                            "Spectral Rolloff": "spec_rolloff",
                            "Spectral Decrease": "spec_decrease",
                            "Normalized Spectral Decrease": "spec_decrease",

                            # High-frequency content
                            "HFC": "hfc",
                            "Normalized HFC": "hfc",  # We'll normalize this manually

                            # Composite descriptors
                            "WOSS": "WOSS"
                        }

                        # Initialize stats for all available outputs
                        for output in AVAILABLE_OUTPUTS_SINGLE + AVAILABLE_OUTPUTS_MULTI:
                            if output not in stats:  # Initialize for all, including Mag Spec
                                stats[output] = {'values': []}

                        # Add custom stats for normalized values
                        stats['Normalized HFC'] = {'values': []}
                        stats['Normalized Spectral Decrease'] = {'values': []}

                        # Removed pre-initialization of _max, _median, _std for spectrograms

                        # Collect values for each descriptor
                        for desc_idx, desc in enumerate(calculated_descriptors): # Use enumerate for logging if needed
                            # Skip if it's an error placeholder or truly empty
                            if not desc or 'error' in desc:
                                if 'error' in desc:
                                     logging.warning(f"Skipping statistics calculation for trace {desc.get('well_marker_name', 'Unknown')} due to previous error: {desc['error']}")
                                else:
                                     logging.warning(f"Skipping statistics calculation for trace index {desc_idx} due to empty descriptor.")
                                continue

                            # Process each available output
                            for output_name, values_dict in stats.items():
                                # Get the corresponding internal key
                                internal_key = descriptor_mapping.get(output_name)

                                # Skip if no mapping or descriptor doesn't exist in current result
                                if not internal_key:
                                    continue
                                if internal_key not in desc:
                                    continue

                                # Aligned Spectrogram statistics (only mean)
                                if output_name == "Magnitude Spectrogram" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                    spec_array = desc[internal_key]
                                    mean_mag_spec = np.mean(spec_array)
                                    if not np.isnan(mean_mag_spec): values_dict['values'].append(mean_mag_spec)
                                elif output_name == "Magnitude * Voice" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                    mag_voice_array = desc[internal_key]
                                    mean_mag_voice = np.mean(mag_voice_array)
                                    if not np.isnan(mean_mag_voice): values_dict['values'].append(mean_mag_voice)
                                # Aligned Normalized HFC calculation
                                elif output_name == 'Normalized HFC' and 'hfc' in desc:
                                    hfc_p95_val = st.session_state.plot_settings.get('hfc_p95', 1.0) # Get from plot_settings
                                    if hfc_p95_val > 0:
                                        normalized_hfc_val = desc['hfc'] / hfc_p95_val
                                        values_dict['values'].extend(normalized_hfc_val if isinstance(normalized_hfc_val, np.ndarray) else [normalized_hfc_val])
                                    else:
                                        logging.warning(f"Invalid hfc_p95 value ({hfc_p95_val}) for Normalized HFC calculation. Skipping normalization for this trace.")
                                elif output_name == 'Normalized Spectral Decrease' and 'spec_decrease' in desc:
                                    spec_decrease_p95_val = st.session_state.plot_settings.get('spec_decrease_p95', 1.0)
                                    if spec_decrease_p95_val > 0:
                                        normalized_spec_decrease_val = desc['spec_decrease'] / spec_decrease_p95_val
                                        values_dict['values'].extend(normalized_spec_decrease_val if isinstance(normalized_spec_decrease_val, np.ndarray) else [normalized_spec_decrease_val])
                                    else:
                                        logging.warning(f"Invalid spec_decrease_p95 value ({spec_decrease_p95_val}) for Normalized Spectral Decrease calculation. Skipping normalization for this trace.")
                                # Handle WOSS calculation if not already present
                                elif output_name == 'WOSS' and 'WOSS' not in desc and all(k in desc for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                    # Ensure we're using the plot_settings with the hfc_p95 value from precomputation
                                    # Initialize woss_params, can be from plot_settings or an empty dict
                                    woss_params = st.session_state.get('plot_settings', {}).copy()

                                    # Get HFC p95 from session state (set in Step 2)
                                    hfc_p95_session_value = st.session_state.get('hfc_p95')

                                    if hfc_p95_session_value is not None:
                                        woss_params['hfc_p95'] = hfc_p95_session_value
                                        logging.info(f"Using HFC p95 from session state: {hfc_p95_session_value}")
                                    elif 'hfc_p95' not in woss_params:
                                        # Fallback: if not in session_state AND not in plot_settings, calculate from current data
                                        logging.warning("HFC p95 value not found in session state or plot_settings. Calculating from current data.")
                                        if 'hfc' in desc and isinstance(desc['hfc'], np.ndarray) and desc['hfc'].size > 0:
                                            hfc_percentile = woss_params.get('hfc_percentile', 95.0) # Default to 95 if not specified
                                            calculated_hfc_p95 = np.percentile(desc['hfc'], hfc_percentile)
                                            woss_params['hfc_p95'] = float(calculated_hfc_p95)
                                            logging.info(f"Calculated HFC p{hfc_percentile} from current data: {calculated_hfc_p95}")
                                        else:
                                            logging.warning("Could not calculate HFC p95 from current data as 'hfc' descriptor is missing or empty.")
                                            # calculate_woss itself has a fallback if hfc_p95 is still missing in woss_params

                                    # Log the parameters being used for WOSS calculation
                                    logging.info(f"WOSS calculation parameters: hfc_p95={woss_params.get('hfc_p95', 'Not set/Fallback in calculate_woss')}, epsilon={woss_params.get('epsilon', 1e-4)}")
                                    woss = calculate_woss(desc, woss_params)
                                    values_dict['values'].extend(woss)
                                # Regular case (for 1D arrays)
                                elif isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 1:
                                    values_dict['values'].extend(desc[internal_key])

                        # Calculate statistics for each descriptor
                        for desc_name, desc_stats in stats.items():
                            if desc_stats['values']:
                                values = np.array(desc_stats['values'])
                                desc_stats['min'] = np.min(values)
                                desc_stats['max'] = np.max(values)
                                desc_stats['p5'] = np.percentile(values, 5)
                                desc_stats['p90'] = np.percentile(values, 90)

                                # HFC p95 value is now calculated and stored in Step 2.
                                # This ensures the same value is used for all subsequent calculations.
                                pass
                            else:
                                desc_stats['min'] = 'N/A'
                                desc_stats['max'] = 'N/A'
                                desc_stats['p5'] = 'N/A'
                                desc_stats['p90'] = 'N/A'

                        # Display the statistics in an expander
                        with st.expander("📊 Descriptor Statistics Summary", expanded=True):
                            st.markdown("### Signal Descriptor Statistics")
                            st.markdown("The following statistics have been calculated for the selected traces:")

                            # Group descriptors into categories for better organization
                            categories = {
                                "Primary Descriptors": [
                                    "Slope Magnitude", "Normalized HFC", "Normalized Spectral Decrease", "Normalized Dominant Frequencies",
                                    "WOSS", "Normalized Dominant Frequency", "Mag*Voice Slope"
                                ],
                                "Spectral Properties": [
                                    "Spectral Slope", "Spectral Bandwidth", "Spectral Rolloff",
                                    "Spectral Decrease", "Magnitude * Voice" # Mean of Mag*Voice will be here
                                ],
                                "Signal Characteristics": [
                                    "Input Signal", "HFC", "Magnitude Spectrogram" # Mean of Mag Spec will be here
                                ],
                                "Other Descriptors": []  # For any remaining descriptors (Aligned with backup/app_ref.py)
                            }

                            # Assign each descriptor to a category
                            categorized_stats = {cat: [] for cat in categories.keys()}

                            for desc_name, desc_stats in stats.items():
                                if not desc_stats['values']:  # Skip empty descriptors
                                    continue

                                # Find which category this descriptor belongs to
                                assigned = False
                                for cat_name, cat_descriptors in categories.items():
                                    if desc_name in cat_descriptors:
                                        if isinstance(desc_stats['min'], (int, float)):
                                            categorized_stats[cat_name].append({
                                                "Descriptor": desc_name,
                                                "Min": desc_stats['min'],  # Keep as numeric
                                                "Max": desc_stats['max'],  # Keep as numeric
                                                "P5": desc_stats['p5'],    # Keep as numeric
                                                "P90": desc_stats['p90']   # Keep as numeric
                                            })
                                        else:
                                            categorized_stats[cat_name].append({
                                                "Descriptor": desc_name,
                                                "Min": desc_stats['min'],
                                                "Max": desc_stats['max'],
                                                "P5": desc_stats['p5'],
                                                "P90": desc_stats['p90']
                                            })
                                        assigned = True
                                        break

                                # If not assigned to any specific category, put in "Other Descriptors"
                                if not assigned and desc_stats['values']:
                                    if isinstance(desc_stats['min'], (int, float)):
                                        categorized_stats["Other Descriptors"].append({
                                            "Descriptor": desc_name,
                                            "Min": desc_stats['min'],  # Keep as numeric
                                            "Max": desc_stats['max'],  # Keep as numeric
                                            "P5": desc_stats['p5'],    # Keep as numeric
                                            "P90": desc_stats['p90']   # Keep as numeric
                                        })
                                    else:
                                        categorized_stats["Other Descriptors"].append({
                                            "Descriptor": desc_name,
                                            "Min": desc_stats['min'],
                                            "Max": desc_stats['max'],
                                            "P5": desc_stats['p5'],
                                            "P90": desc_stats['p90']
                                        })

                            # Display tables by category
                            for cat_name, cat_stats in categorized_stats.items():
                                if cat_stats:  # Only show categories with data
                                    st.subheader(cat_name)
                                    # Create DataFrame with numeric values
                                    df = pd.DataFrame(cat_stats)

                                    # Format the DataFrame for display with 4 decimal places
                                    # but keep the underlying data as numeric
                                    formatted_df = df.style.format({
                                        "Min": "{:.4f}",
                                        "Max": "{:.4f}",
                                        "P5": "{:.4f}",
                                        "P90": "{:.4f}"
                                    }, na_rep="N/A")

                                    st.table(formatted_df)

                            # Add explanation
                            st.markdown("""
                            **Note:**
                            - **Min/Max**: Absolute minimum and maximum values across all traces
                            - **P5**: 5th percentile value (95% of values are above this)
                            - **P90**: 90th percentile value (10% of values are above this)
                            """)

                        # Store the statistics in session state for later use
                        st.session_state.descriptor_statistics = stats
                    # End of "Grouping Well Analysis" specific logic
                
                    # Common "View Results" button, enabled if analysis_complete is True
                    if st.session_state.get('analysis_complete', False):
                        st.markdown("---")
                        st.success("Analysis processing is complete.")
                        if st.button("View Results", key="view_results_button_well_markers", help="Proceed to the results page"):
                            st.session_state.current_step = "view_results"
                            st.rerun()
            else:
                st.warning("Please select at least one well-marker pair.")

        # This 'else' corresponds to 'if st.session_state.well_df is not None ...'
        else:
            st.markdown("---")
            st.success("✅ Descriptor calculation is complete. You can now view the detailed results.")
            if st.button("View Results", key="view_results_button_main", help="Open the detailed analysis report"):
                st.session_state.current_step = "view_results"
                st.rerun()
    else:
        st.error("No well data available. Please go back and upload well data.")
        if st.button("Back to Mode Selection"):
            st.session_state.current_step = "select_mode"
            st.rerun()

    # AOI Mode
    if st.session_state.selection_mode == "By inline/crossline section (AOI)":
        # Process AOI selection
        st.info("Processing AOI selection...")

        # Get the selected AOI
        inline_min = st.session_state.aoi_inline_min
        inline_max = st.session_state.aoi_inline_max
        xline_min = st.session_state.aoi_xline_min
        xline_max = st.session_state.aoi_xline_max

        # Find traces within the AOI
        with st.spinner("Finding traces within the AOI..."):
            try:
                # Get header dataframe
                headers_df = pd.DataFrame({
                    'inline': st.session_state.header_loader.inlines,
                    'crossline': st.session_state.header_loader.crosslines,
                    'x': st.session_state.header_loader.x_coords,
                    'y': st.session_state.header_loader.y_coords
                })
                headers_df['trace_idx'] = st.session_state.header_loader.unique_indices

                # Filter by inline/crossline range
                aoi_df = headers_df[
                    (headers_df['inline'] >= inline_min) &
                    (headers_df['inline'] <= inline_max) &
                    (headers_df['crossline'] >= xline_min) &
                    (headers_df['crossline'] <= xline_max) # Completed the condition
                ]

                st.session_state.selected_indices = aoi_df['trace_idx'].tolist()
                st.success(f"Found {len(st.session_state.selected_indices)} traces within the specified AOI.")

                # Add a button to proceed to analysis
                st.markdown("---")
                st.markdown("### Next Steps:")
                st.markdown("1. Click **Calculate Descriptors** to process the selected traces and compute signal descriptors")
                st.markdown("2. Review the statistical summary that will appear")
                st.markdown("3. Click **View Results** to proceed to the results page for visualization and analysis options")

                if st.button("Calculate Descriptors", key="calculate_descriptors_button", help="Process the selected traces and compute spectral descriptors"):
                    # Calculate descriptors for the selected AOI traces
                    with st.spinner("Calculating spectral descriptors for AOI..."):
                        # Load trace data for the selected indices
                        loaded_trace_data = []
                        max_len = 0
                        for trace_idx in st.session_state.selected_indices:
                            # Check if we have pre-computed data for this trace
                            if precomputed_data:
                                precomputed_item = next((item for item in precomputed_data if item.get('trace_idx') == trace_idx), None)
                                if precomputed_item and 'processed_trace' in precomputed_item:
                                    # Use the processed trace from pre-computation
                                    trace_sample = precomputed_item['processed_trace']
                                    logging.info(f"Using pre-computed data for trace {trace_idx}")
                                else:
                                    # Fallback to loading the original trace
                                    trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                                    logging.info(f"Pre-computed data not found for trace {trace_idx}, using original data")
                            else:
                                # No precomputed data available, load the original trace
                                trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                                logging.info(f"Loading original data for trace {trace_idx} (no precomputation)")

                            loaded_trace_data.append({'trace_sample': trace_sample, 'trace_idx': trace_idx})
                            max_len = max(max_len, len(trace_sample))

                        # Pad traces if necessary
                        if max_len > 0:
                            for item in loaded_trace_data:
                                if len(item['trace_sample']) < max_len:
                                    pad_width = max_len - len(item['trace_sample'])
                                    item['trace_sample'] = np.pad(item['trace_sample'], (0, pad_width), 'constant')

                        # Stack traces into a 2D array for batch processing
                        if loaded_trace_data:
                            traces_array = np.stack([t['trace_sample'] for t in loaded_trace_data]).astype(np.float32)
                            fmax_calc = max_len // 2 if max_len > 0 else 250

                            # Use the descriptor settings from plot_settings
                            descriptor_settings = {
                                'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                                'shape': st.session_state.plot_settings.get('shape', 0.35),
                                'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                                'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                                'b1': st.session_state.plot_settings.get('b1', 5.0),
                                'b2': st.session_state.plot_settings.get('b2', 40.0),
                                'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                                'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80),
                                'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4),
                                'hfc_percentile': st.session_state.plot_settings.get('hfc_percentile', 95.0)
                            }

                            try:
                                if st.session_state.get('GPU_AVAILABLE', False):
                                    try:
                                        # Remove WOSS-specific parameters for GPU function
                                        gpu_descriptor_settings = {
                                            k: v for k, v in descriptor_settings.items()
                                            if k not in ['epsilon', 'fdom_exponent', 'hfc_p95', 'hfc_percentile']
                                        }

                                        # Log parameters for debugging
                                        logging.info(f"Calling dlogst_spec_descriptor_gpu_2d_chunked_mag with parameters: dt={st.session_state.dt}, fmax={fmax_calc}, batch_size={st.session_state.get('batch_size', 512)}, settings={gpu_descriptor_settings}")

                                        # Use the 2D chunked GPU function
                                        calculated_descriptors_dict = dlogst_spec_descriptor_gpu_2d_chunked_mag(
                                            traces_array,
                                            st.session_state.dt,
                                            fmax=fmax_calc,
                                            batch_size=st.session_state.get('batch_size', 512), # Use batch size from session state
                                            **gpu_descriptor_settings
                                        )

                                        # Verify that we got results back
                                        if not calculated_descriptors_dict:
                                            st.error("GPU calculation returned empty results. Check logs for details.")
                                            logging.error("GPU calculation returned empty results.")
                                            raise ValueError("Empty descriptor results from GPU calculation")

                                        # Log the keys we got back
                                        logging.info(f"Received descriptor keys: {list(calculated_descriptors_dict.keys())}")

                                        # Convert dictionary of arrays to list of dictionaries
                                        num_traces_in_batch = traces_array.shape[0]
                                        calculated_descriptors = [{} for _ in range(num_traces_in_batch)]
                                        for key, value_array in calculated_descriptors_dict.items():
                                            if isinstance(value_array, np.ndarray) and value_array.shape[0] == num_traces_in_batch:
                                                for trace_idx in range(num_traces_in_batch):
                                                    calculated_descriptors[trace_idx][key] = value_array[trace_idx]
                                            # Handle 2D arrays like 'mag' and 'mag_voice'
                                            elif isinstance(value_array, np.ndarray) and value_array.ndim == 3 and value_array.shape[0] == num_traces_in_batch:
                                                for trace_idx in range(num_traces_in_batch):
                                                    calculated_descriptors[trace_idx][key] = value_array[trace_idx]
                                            else:
                                                logging.warning(f"Shape mismatch for descriptor '{key}' in AOI processing. Expected {num_traces_in_batch} traces, got {value_array.shape if isinstance(value_array, np.ndarray) else 'not an array'}. Skipping this descriptor.")

                                        # Verify that descriptors contain expected keys
                                        expected_keys = ['data', 'peak_freq', 'spec_centroid', 'fdom', 'norm_fdom', 'hfc', 'mag_voice_slope']
                                        if calculated_descriptors:
                                            missing_keys = [key for key in expected_keys if key not in calculated_descriptors[0]]
                                            if missing_keys:
                                                logging.warning(f"Descriptors are missing expected keys: {missing_keys}")
                                                st.warning(f"Descriptor calculation incomplete. Missing: {', '.join(missing_keys)}")

                                    except Exception as e:
                                        logging.error(f"Error in GPU batch processing: {e}", exc_info=True)
                                        st.error(f"GPU batch processing error: {str(e)}")
                                        # Fall back to individual processing
                                        st.warning("Falling back to individual trace processing...")
                                        calculated_descriptors = []
                                        for trace_item in tqdm(loaded_trace_data, desc="Processing traces individually"):
                                            try:
                                                # Remove WOSS-specific parameters for GPU function
                                                gpu_descriptor_settings = {
                                                    k: v for k, v in descriptor_settings.items()
                                                    if k not in ['epsilon', 'fdom_exponent', 'hfc_p95', 'hfc_percentile']
                                                }

                                                descriptor = dlogst_spec_descriptor_gpu(
                                                    trace_item['trace_sample'],
                                                    st.session_state.dt,
                                                    fmax=len(trace_item['trace_sample']) // 2,
                                                    **gpu_descriptor_settings
                                                )
                                                calculated_descriptors.append(descriptor)
                                            except Exception as e_inner:
                                                logging.error(f"Error processing trace {trace_item['trace_idx']}: {e_inner}")
                                                calculated_descriptors.append({})

                                else:
                                    st.warning("GPU not available. Processing AOI on CPU (very slow)...")
                                    # Fallback to processing traces individually on CPU
                                    calculated_descriptors = []
                                    try:
                                        # Try to import CPU implementation if available
                                        from utils.dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu

                                        for trace_item in tqdm(loaded_trace_data, desc="Processing traces on CPU"):
                                            try:
                                                descriptor = dlogst_spec_descriptor_cpu(
                                                    trace_item['trace_sample'],
                                                    st.session_state.dt,
                                                    fmax=len(trace_item['trace_sample']) // 2,
                                                    **descriptor_settings
                                                )
                                                calculated_descriptors.append(descriptor)
                                            except Exception as e:
                                                logging.error(f"Error calculating descriptor for trace {trace_item['trace_idx']} on CPU: {e}")
                                                calculated_descriptors.append({})
                                    except ImportError:
                                        st.error("CPU implementation not available. Cannot calculate descriptors.")
                                        # Create empty descriptors as fallback
                                        calculated_descriptors = [{} for _ in range(len(loaded_trace_data))]
                                    except Exception as e:
                                        logging.error(f"Error in CPU processing setup: {e}", exc_info=True)
                                        st.error(f"CPU processing error: {str(e)}")
                                        calculated_descriptors = [{} for _ in range(len(loaded_trace_data))]

                                # Check if we have valid descriptors
                                valid_descriptors = [d for d in calculated_descriptors if d and len(d) > 0]
                                if valid_descriptors:
                                    st.session_state.calculated_descriptors = calculated_descriptors
                                    st.session_state.analysis_complete = True
                                    st.success(f"Descriptor calculation complete for AOI. {len(valid_descriptors)} of {len(calculated_descriptors)} traces processed successfully.")
                                else:
                                    st.error("No valid descriptors were calculated. Please check the logs for errors.")
                                    st.session_state.calculated_descriptors = []
                                    st.session_state.analysis_complete = False

                                # Calculate statistics for the descriptors (same logic as well markers mode)
                                if calculated_descriptors:
                                    # Initialize dictionaries to store statistics for all available outputs
                                    stats = {}

                                    # Create mapping between display names and internal descriptor keys
                                    descriptor_mapping = {
                                        # Signal data
                                        "Input Signal": "data",

                                        # Spectrograms and time-frequency representations
                                        "Magnitude Spectrogram": "mag", # Use 'mag' key
                                        "Magnitude * Voice": "mag_voice", # Use 'mag_voice' key

                                        # Frequency-related descriptors
                                        "Normalized dominant frequencies": "norm_fdom",
                                        "Normalized Dominant Frequency": "norm_fdom",
                                        "Dominant Frequency": "fdom",
                                        "Peak Frequency": "peak_freq",
                                        "Spectral Centroid": "spec_centroid",

                                        # Slope-related descriptors
                                        "Spectral Slope": "spec_slope",
                                        "Mag*Voice Slope": "mag_voice_slope",
                                        "Voice Slope": "voice_slope",
                                        "Slope Magnitude": "mag_voice_slope",

                                        # Bandwidth and spectral shape descriptors
                                        "Spectral Bandwidth": "spec_bandwidth",
                                        "Spectral Rolloff": "spec_rolloff",
                                        "Spectral Decrease": "spec_decrease",

                                        # High-frequency content
                                        "HFC": "hfc",
                                        "Normalized HFC": "hfc",  # We'll normalize this manually

                                        # Composite descriptors
                                        "WOSS": "WOSS"
                                    }

                                    # Initialize stats for all available outputs
                                    for output in AVAILABLE_OUTPUTS_SINGLE + AVAILABLE_OUTPUTS_MULTI:
                                        if output not in stats:
                                            stats[output] = {'values': []}

                                    # Add custom stats for normalized values
                                    stats['Normalized HFC'] = {'values': []}

                                    # Pre-initialize additional statistics for spectrograms to avoid modifying dict during iteration
                                    stats['Magnitude Spectrogram_max'] = {'values': []}
                                    stats['Magnitude Spectrogram_median'] = {'values': []}
                                    stats['Magnitude Spectrogram_std'] = {'values': []}
                                    stats['Magnitude * Voice_max'] = {'values': []}
                                    stats['Magnitude * Voice_median'] = {'values': []}
                                    stats['Magnitude * Voice_std'] = {'values': []}

                                    # Collect values for each descriptor
                                    for desc in calculated_descriptors:
                                        if not desc:  # Skip empty descriptors
                                            continue

                                        # Process each available output
                                        for output_name, values_dict in stats.items():
                                            # Get the corresponding internal key
                                            internal_key = descriptor_mapping.get(output_name)

                                            # Skip if no mapping or descriptor doesn't exist in current result
                                            if not internal_key:
                                                continue
                                            if internal_key not in desc:
                                                continue

                                            # Enhanced statistics for 2D Spectrogram (handling 3D dimensional arrays for each trace)
                                            if output_name == "Magnitude Spectrogram" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                                # Calculate multiple statistics for the 2D array
                                                spec_array = desc[internal_key]
                                                # Mean across all values
                                                mean_mag_spec = np.mean(spec_array)
                                                if not np.isnan(mean_mag_spec): values_dict['values'].append(mean_mag_spec)

                                                # These keys should already be pre-initialized before the loop
                                                # No need to check if they exist

                                                # Calculate additional statistics
                                                max_val = np.max(spec_array)
                                                median_val = np.median(spec_array)
                                                std_val = np.std(spec_array)

                                                # Store these statistics
                                                if not np.isnan(max_val): stats[f"{output_name}_max"]['values'].append(max_val)
                                                if not np.isnan(median_val): stats[f"{output_name}_median"]['values'].append(median_val)
                                                if not np.isnan(std_val): stats[f"{output_name}_std"]['values'].append(std_val)

                                            # Enhanced statistics for Magnitude * Voice (handling 3D dimensional arrays for each trace)
                                            elif output_name == "Magnitude * Voice" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                                # Calculate multiple statistics for the 2D array
                                                mag_voice_array = desc[internal_key]
                                                # Mean across all values
                                                mean_mag_voice = np.mean(mag_voice_array)
                                                if not np.isnan(mean_mag_voice): values_dict['values'].append(mean_mag_voice)

                                                # These keys should already be pre-initialized before the loop
                                                # No need to check if they exist

                                                # Calculate additional statistics
                                                max_val = np.max(mag_voice_array)
                                                median_val = np.median(mag_voice_array)
                                                std_val = np.std(mag_voice_array)

                                                # Store these statistics
                                                if not np.isnan(max_val): stats[f"{output_name}_max"]['values'].append(max_val)
                                                if not np.isnan(median_val): stats[f"{output_name}_median"]['values'].append(median_val)
                                                if not np.isnan(std_val): stats[f"{output_name}_std"]['values'].append(std_val)
                                            # Special case for normalized HFC
                                            elif output_name == 'Normalized HFC' and 'hfc' in desc:
                                                # Use the hfc_p95 value calculated during precomputation
                                                hfc_p95 = st.session_state.get('hfc_p95', 1.0) # Get from session state, default to 1.0 if not found
                                                if hfc_p95 > 0:
                                                    normalized_hfc = desc['hfc'] / hfc_p95
                                                    values_dict['values'].extend(normalized_hfc)
                                                else:
                                                    logging.warning("Invalid hfc_p95 value for normalization. Using max value instead.")
                                                    hfc_max = np.max(np.abs(desc['hfc']))
                                                    normalized_hfc = desc['hfc'] / (hfc_max + 1e-4)  # Add small epsilon
                                                    values_dict['values'].extend(normalized_hfc)
                                            # Handle WOSS calculation if not already present
                                            elif output_name == 'WOSS' and 'WOSS' not in desc and all(k in desc for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                                # Ensure we're using the plot_settings with the hfc_p95 value from precomputation
                                                # Initialize woss_params, can be from plot_settings or an empty dict
                                                woss_params = st.session_state.get('plot_settings', {}).copy()

                                                # Get HFC p95 from session state (set in Step 2)
                                                hfc_p95_session_value = st.session_state.get('hfc_p95')

                                                if hfc_p95_session_value is not None:
                                                    woss_params['hfc_p95'] = hfc_p95_session_value
                                                    logging.info(f"Using HFC p95 from session state for WOSS: {hfc_p95_session_value}")
                                                elif 'hfc_p95' not in woss_params:
                                                    # Fallback: if not in session_state AND not in plot_settings, calculate_woss has its own internal fallback
                                                    logging.warning("HFC p95 value not found in session state or plot_settings for WOSS. Relaying on calculate_woss fallback.")

                                                # Log the parameters being used for WOSS calculation
                                                logging.info(f"WOSS calculation parameters: hfc_p95={woss_params.get('hfc_p95', 'Not set/Fallback in calculate_woss')}, epsilon={woss_params.get('epsilon', 1e-4)}")
                                                woss = calculate_woss(desc, woss_params)
                                                values_dict['values'].extend(woss)
                                            # Regular case (for 1D arrays)
                                            elif isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 1:
                                                values_dict['values'].extend(desc[internal_key])

                                    # Calculate statistics for each descriptor
                                    for desc_name, desc_stats in stats.items():
                                        if desc_stats['values']:
                                            values = np.array(desc_stats['values'])
                                            desc_stats['min'] = np.min(values)
                                            desc_stats['max'] = np.max(values)
                                            desc_stats['p5'] = np.percentile(values, 5)
                                            desc_stats['p90'] = np.percentile(values, 90)
                                        else:
                                            desc_stats['min'] = 'N/A'
                                            desc_stats['max'] = 'N/A'
                                            desc_stats['p5'] = 'N/A'
                                            desc_stats['p90'] = 'N/A'

                                    # Display the statistics in an expander
                                    with st.expander("📊 Descriptor Statistics Summary", expanded=True):
                                        st.markdown("### Signal Descriptor Statistics")
                                        st.markdown("The following statistics have been calculated for the selected traces:")

                                        # Group descriptors into categories for better organization
                                        categories = {
                                            "Primary Descriptors": [
                                                "Slope Magnitude", "Normalized HFC", "Normalized Dominant Frequencies",
                                                "WOSS", "Normalized Dominant Frequency", "Mag*Voice Slope"
                                            ],
                                            "Spectral Properties": [
                                                "Spectral Slope", "Spectral Bandwidth", "Spectral Rolloff",
                                                "Spectral Decrease", "Magnitude * Voice"
                                            ],
                                            "Signal Characteristics": [
                                                "Input Signal", "HFC", "Magnitude Spectrogram"
                                            ],
                                            "Spectrogram Statistics": [
                                                "Magnitude Spectrogram_max", "Magnitude Spectrogram_median", "Magnitude Spectrogram_std",
                                                "Magnitude * Voice_max", "Magnitude * Voice_median", "Magnitude * Voice_std"
                                            ],
                                            "Other Descriptors": []  # For any remaining descriptors
                                        }

                                        # Assign each descriptor to a category
                                        categorized_stats = {cat: [] for cat in categories.keys()}

                                        for desc_name, desc_stats in stats.items():
                                            if not desc_stats['values']:  # Skip empty descriptors
                                                continue

                                            # Find which category this descriptor belongs to
                                            assigned = False
                                            for cat_name, cat_descriptors in categories.items():
                                                if desc_name in cat_descriptors:
                                                    if isinstance(desc_stats['min'], (int, float)):
                                                        categorized_stats[cat_name].append({
                                                            "Descriptor": desc_name,
                                                            "Min": desc_stats['min'],  # Keep as numeric
                                                            "Max": desc_stats['max'],  # Keep as numeric
                                                            "P5": desc_stats['p5'],    # Keep as numeric
                                                            "P90": desc_stats['p90']   # Keep as numeric
                                                        })
                                                    else:
                                                        categorized_stats[cat_name].append({
                                                            "Descriptor": desc_name,
                                                            "Min": desc_stats['min'],
                                                            "Max": desc_stats['max'],
                                                            "P5": desc_stats['p5'],
                                                            "P90": desc_stats['p90']
                                                        })
                                                    assigned = True
                                                    break

                                            # If not assigned to any specific category, put in "Other Descriptors"
                                            if not assigned and desc_stats['values']:
                                                if isinstance(desc_stats['min'], (int, float)):
                                                    categorized_stats["Other Descriptors"].append({
                                                        "Descriptor": desc_name,
                                                        "Min": desc_stats['min'],  # Keep as numeric
                                                        "Max": desc_stats['max'],  # Keep as numeric
                                                        "P5": desc_stats['p5'],    # Keep as numeric
                                                        "P90": desc_stats['p90']   # Keep as numeric
                                                    })
                                                else:
                                                    categorized_stats["Other Descriptors"].append({
                                                        "Descriptor": desc_name,
                                                        "Min": desc_stats['min'],
                                                        "Max": desc_stats['max'],
                                                        "P5": desc_stats['p5'],
                                                        "P90": desc_stats['p90']
                                                    })

                                        # Display tables by category
                                        for cat_name, cat_stats in categorized_stats.items():
                                            if cat_stats:  # Only show categories with data
                                                st.subheader(cat_name)
                                                # Create DataFrame with numeric values
                                                df = pd.DataFrame(cat_stats)

                                                # Format the DataFrame for display with 4 decimal places
                                                # but keep the underlying data as numeric
                                                formatted_df = df.style.format({
                                                    "Min": "{:.4f}",
                                                    "Max": "{:.4f}",
                                                    "P5": "{:.4f}",
                                                    "P90": "{:.4f}"
                                                }, na_rep="N/A")

                                                st.table(formatted_df)

                                        # Add explanation
                                        st.markdown("""
                                        **Note:**
                                        - **Min/Max**: Absolute minimum and maximum values across all traces
                                        - **P5**: 5th percentile value (95% of values are above this)
                                        - **P90**: 90th percentile value (10% of values are above this)
                                        """)

                                    # Store the statistics in session state for later use
                                    st.session_state.descriptor_statistics = stats

                                # Proceed to results - only show this button after descriptors are calculated
                                if st.session_state.analysis_complete:
                                    st.markdown("---")
                                    st.success("✅ Descriptor calculation is complete for AOI. You can now view the detailed results.")
                                    if st.button("View Results", key="view_results_button_aoi", help="Open the detailed analysis report"):
                                        st.session_state.current_step = "view_results"
                                        st.rerun()

                            except Exception as e:
                                st.error(f"Error calculating descriptors for AOI: {e}")
                                logging.error(f"Descriptor calculation failed for AOI: {e}", exc_info=True)

            except Exception as e:
                st.error(f"Error finding traces within AOI: {e}")
                logging.error(f"AOI trace finding failed: {e}", exc_info=True)

    # Single Inline Mode
    elif st.session_state.selection_mode == "Single inline (all crosslines)":
        st.subheader("Selected Area Information")
        # Use selected_inline_number if available, otherwise use selected_inline
        selected_inline = st.session_state.selected_inline_number if 'selected_inline_number' in st.session_state else st.session_state.selected_inline
        st.write(f"Selected inline: {selected_inline}")
        st.write(f"Number of selected traces: {len(st.session_state.selected_indices)}")

        # Check if we have loaded trace data
        loaded_trace_data = st.session_state.get('loaded_trace_data', [])

        if not loaded_trace_data:
            # Load trace data for the selected inline
            with st.spinner(f"Loading traces for inline {selected_inline}..."):
                loaded_trace_data = []
                max_len = 0
                try:
                    for trace_idx in st.session_state.selected_indices:
                        # Load the trace sample
                        trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                        loaded_trace_data.append({'trace_sample': trace_sample, 'trace_idx': trace_idx})
                        max_len = max(max_len, len(trace_sample))

                    # Pad traces if necessary
                    if max_len > 0:
                        for item in loaded_trace_data:
                            if len(item['trace_sample']) < max_len:
                                pad_width = max_len - len(item['trace_sample'])
                                item['trace_sample'] = np.pad(item['trace_sample'], (0, pad_width), 'constant')

                    st.session_state.loaded_trace_data = loaded_trace_data
                    st.success(f"Successfully loaded {len(loaded_trace_data)} traces for inline {selected_inline}.")
                except Exception as e:
                    st.error(f"Error loading traces: {e}")
                    logging.error(f"Error loading traces for inline {selected_inline}: {e}", exc_info=True)
        else:
            st.success(f"Loaded {len(loaded_trace_data)} traces for analysis.")

        # Add a button to calculate descriptors
        st.markdown("---")
        st.markdown("### Next Steps:")
        st.markdown("1. Click **Calculate Descriptors** to process the selected traces and compute signal descriptors")
        st.markdown("2. Review the statistical summary that will appear")
        st.markdown("3. Click **View Results** to proceed to the results page for visualization and analysis options")

        if not st.session_state.get('GPU_AVAILABLE', False):
            st.error("GPU processing is required for this analysis mode, but no GPU is available. Please check your system configuration.")
        elif st.button("Calculate Descriptors", key="calculate_descriptors_button_inline", help="Process the selected traces and compute spectral descriptors"):
            if not loaded_trace_data:
                st.warning("No traces loaded to calculate descriptors.")
                return

            # Stack traces into a 2D array
            trace_samples = [item['trace_sample'] for item in loaded_trace_data]
            max_len = max(len(trace) for trace in trace_samples)
            padded_traces = [np.pad(trace, (0, max_len - len(trace)), 'constant') for trace in trace_samples]
            traces_array = np.stack(padded_traces).astype(np.float32)

            # Get descriptor settings from plot_settings
            descriptor_settings = {
                'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                'shape': st.session_state.plot_settings.get('shape', 0.35),
                'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                'b1': st.session_state.plot_settings.get('b1', 5.0),
                'b2': st.session_state.plot_settings.get('b2', 40.0),
                'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80),
            }

            # Calculate descriptors using GPU
            progress_bar = st.progress(0)
            status_text = st.empty()
            status_text.text("Initializing descriptor calculation...")

            with st.spinner("Calculating spectral descriptors using GPU..."):
                try:
                    # Use the 2D chunked GPU function
                    fmax_calc = max_len // 2 if max_len > 0 else 250
                    calculated_descriptors_dict = dlogst_spec_descriptor_gpu_2d_chunked(
                        traces_array,
                        st.session_state.dt,
                        fmax=fmax_calc,
                        batch_size=st.session_state.get('batch_size', 512),
                        **descriptor_settings
                    )

                    # Calculate WOSS if needed
                    if 'hfc' in calculated_descriptors_dict and 'norm_fdom' in calculated_descriptors_dict and 'mag_voice_slope' in calculated_descriptors_dict:
                        # Get HFC p95 from session state
                        hfc_p95 = st.session_state.get('hfc_p95', 1.0)

                        # Create WOSS parameters
                        woss_params = {
                            'hfc_p95': hfc_p95,
                            'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4),
                            'fdom_exponent': st.session_state.plot_settings.get('fdom_exponent', 2.0)
                        }

                        # Calculate WOSS for each trace
                        woss_array = np.zeros_like(calculated_descriptors_dict['hfc'])
                        for i in range(traces_array.shape[0]):
                            trace_components = {
                                'hfc': calculated_descriptors_dict['hfc'][i],
                                'norm_fdom': calculated_descriptors_dict['norm_fdom'][i],
                                'mag_voice_slope': calculated_descriptors_dict['mag_voice_slope'][i]
                            }
                            woss_array[i] = calculate_woss(trace_components, woss_params)

                        # Add WOSS to the calculated descriptors
                        calculated_descriptors_dict['WOSS'] = woss_array

                    # Store the calculated descriptors in session state
                    st.session_state.calculated_descriptors = calculated_descriptors_dict
                    st.session_state.analysis_complete = True
                    st.success("Descriptor calculation complete.")

                    # Add a button to view results
                    st.markdown("---")
                    st.success("✅ Descriptor calculation is complete. You can now view the detailed results.")
                    if st.button("View Results", key="view_results_button_inline", help="Open the detailed analysis report"):
                        st.session_state.current_step = "view_results"
                        st.rerun()

                except Exception as e:
                    st.error(f"Error calculating descriptors: {e}")
                    logging.error(f"Error calculating descriptors for inline {selected_inline}: {e}", exc_info=True)
                    return

    # Single Crossline Mode
    if st.session_state.selection_mode == "Single crossline (all inlines)":
        st.subheader("Single Crossline Mode")
        # ... (rest of the Single Crossline Mode logic)
        st.info("Single Crossline Mode logic goes here.") # Placeholder

    # Polyline Mode
    if st.session_state.selection_mode == "By Polyline File Import":
        st.subheader("Polyline Mode")
        
        # Check if we have selected indices
        if not st.session_state.get('selected_indices'):
            st.warning("No traces selected along the polyline. Please go back to Step 3 and select traces.")
            return
            
        # Initialize traces_loaded_polyline if not present
        if 'traces_loaded_polyline' not in st.session_state:
            st.session_state.traces_loaded_polyline = False
            
        # Initialize loaded_trace_data if not present
        if 'loaded_trace_data' not in st.session_state:
            st.session_state.loaded_trace_data = []
            
        st.info(f"Selected {len(st.session_state.selected_indices)} traces along the polyline.")
        
        # Button to load traces if not loaded yet
        if not st.session_state.traces_loaded_polyline:
            if st.button("Load Traces for Processing", key="load_traces_polyline_btn"):
                with st.spinner(f"Loading {len(st.session_state.selected_indices)} traces near polyline..."):
                    try:
                        loaded_data_temp = []
                        # Load each trace
                        for trace_idx in st.session_state.selected_indices:
                            trace_sample = load_trace_sample(st.session_state.segy_temp_file_path, trace_idx)
                            loaded_data_temp.append({
                                'trace_index': trace_idx,
                                'trace_sample': trace_sample
                            })
                        # Store in session state
                        st.session_state.loaded_trace_data = loaded_data_temp
                        st.session_state.traces_loaded_polyline = True
                        st.success(f"Successfully loaded {len(loaded_data_temp)} traces for analysis.")
                        st.rerun()  # Rerun to update the UI
                    except Exception as e:
                        st.error(f"Error loading traces: {e}")
                        st.session_state.traces_loaded_polyline = False
                        logging.error(f"Error loading traces for polyline: {e}", exc_info=True)
        else:
            st.success(f"Loaded {len(st.session_state.loaded_trace_data)} traces for analysis.")
            
            # Select outputs for the section
            st.subheader("Select Outputs for Display")
            available_outputs = AVAILABLE_OUTPUTS_SECTION  # Use the section outputs defined in constants.py
            
            st.session_state.selected_outputs = st.multiselect(
                "Select spectral descriptors to calculate",
                options=available_outputs,
                default=["WOSS"] if "WOSS" in available_outputs else [available_outputs[0]]
            )
            
            # Check if we need to calculate descriptors
            calculate_button_disabled = not st.session_state.traces_loaded_polyline
            if calculate_button_disabled:
                st.warning("Please load traces before calculating descriptors.")
            
            # GPU batch size setting
            if GPU_AVAILABLE:
                suggested_batch_size, gpu_memory_mb = get_suggested_batch_size()
                st.sidebar.subheader("GPU Settings")
                st.session_state.batch_size = st.sidebar.slider(
                    "GPU Batch Size",
                    min_value=1,
                    max_value=max(100, suggested_batch_size * 2),
                    value=suggested_batch_size,
                    step=1,
                    help="Number of traces to process in each GPU batch. Higher values use more GPU memory."
                )
                st.sidebar.info(f"Available GPU Memory: {gpu_memory_mb:.1f} MB")
            
            # Button to calculate descriptors
            if st.button("Calculate Descriptors", key="calculate_descriptors_polyline", disabled=calculate_button_disabled):
                with st.spinner("Calculating spectral descriptors for the polyline section..."):
                    try:
                        # Prepare 2D array from loaded_trace_data
                        trace_samples_list = [item['trace_sample'] for item in st.session_state.loaded_trace_data]
                        section_data_2d = np.stack(trace_samples_list, axis=0)
                        
                        # Get spectral parameters from plot_settings
                        spectral_params = {
                            'winlen': st.session_state.plot_settings.get('winlen', 64),
                            'overlap': st.session_state.plot_settings.get('overlap', 0.5),
                            'fs': 1.0 / st.session_state.dt,  # Calculate sampling frequency from dt
                            'fmin': st.session_state.plot_settings.get('fmin', 0),
                            'fmax': st.session_state.plot_settings.get('fmax', None),
                        }
                        
                        # Determine which outputs to calculate
                        internal_outputs_map = {
                            "Input Signal": "data",
                            "Magnitude Spectrogram": "mag",
                            "Magnitude * Voice": "mag_voice",
                            "Normalized dominant frequencies": "norm_fdom",
                            "Spectral Slope": "spec_slope",
                            "Spectral Bandwidth": "spec_bandwidth", 
                            "Spectral Rolloff": "spec_rolloff",
                            "Mag*Voice Slope": "mag_voice_slope",
                            "Spectral Decrease": "spec_decrease",
                            "HFC": "hfc",
                            "WOSS": "WOSS"
                        }
                        
                        # Map selected outputs to internal names
                        outputs_for_gpu_calc = [internal_outputs_map.get(output, output) for output in st.session_state.selected_outputs]
                        
                        # Ensure we have what we need for WOSS if it's selected
                        if "WOSS" in outputs_for_gpu_calc:
                            required_for_woss = ["hfc", "norm_fdom", "mag_voice_slope"]
                            for required in required_for_woss:
                                if required not in outputs_for_gpu_calc:
                                    outputs_for_gpu_calc.append(required)
                        
                        # Calculate using GPU if available
                        if GPU_AVAILABLE and st.session_state.batch_size:
                            all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked(
                                section_data_2d,
                                st.session_state.dt,
                                batch_size=st.session_state.batch_size,
                                **spectral_params
                            )

                            # Filter to only the requested descriptors
                            calculated_attribute_sections = {key: all_descriptors[key] for key in outputs_for_gpu_calc
                                                            if key in all_descriptors}
                        else:
                            st.error("GPU processing is not available. Please ensure GPU functions are loaded or reduce the data size.")
                            return
                        
                        # Add original data if selected
                        if "data" in outputs_for_gpu_calc and "data" not in calculated_attribute_sections:
                            calculated_attribute_sections["data"] = section_data_2d
                        
                        # Calculate WOSS if selected
                        if "WOSS" in outputs_for_gpu_calc and "WOSS" not in calculated_attribute_sections:
                            if all(key in calculated_attribute_sections for key in ["hfc", "norm_fdom", "mag_voice_slope"]):
                                # Get HFC p95 from session state
                                hfc_p95 = st.session_state.get('hfc_p95', 1.0)
                                
                                # Create WOSS parameters
                                woss_params = {
                                    'hfc_p95': hfc_p95,
                                    'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4),
                                    'fdom_exponent': st.session_state.plot_settings.get('fdom_exponent', 2.0)
                                }
                                
                                # Calculate WOSS for each trace in the section
                                woss_array = np.zeros_like(calculated_attribute_sections['hfc'])
                                for i in range(section_data_2d.shape[0]):
                                    trace_components = {
                                        'hfc': calculated_attribute_sections['hfc'][i],
                                        'norm_fdom': calculated_attribute_sections['norm_fdom'][i],
                                        'mag_voice_slope': calculated_attribute_sections['mag_voice_slope'][i]
                                    }
                                    woss_array[i] = calculate_woss(trace_components, woss_params)
                                
                                # Add WOSS to the calculated descriptors
                                calculated_attribute_sections['WOSS'] = woss_array
                        
                        # Store results in session state
                        st.session_state.calculated_descriptors = calculated_attribute_sections
                        st.session_state.analysis_complete = True
                        st.success("Descriptor calculation complete.")
                        
                        # Add a button to view results
                        st.markdown("---")
                        st.success("✅ Descriptor calculation is complete. You can now view the detailed results.")
                        if st.button("View Results", key="view_results_button_polyline", help="Open the detailed analysis report"):
                            st.session_state.current_step = "view_results"
                            st.rerun()
                            
                    except Exception as e:
                        st.error(f"Error calculating descriptors: {e}")
                        logging.error(f"Error calculating descriptors for polyline section: {e}", exc_info=True)
                        return

    # Add a "Start New Analysis" button to the sidebar
    st.sidebar.markdown("---")
    if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
        reset_state()
        st.success("Starting new analysis. All temporary data has been cleared.")
        st.rerun()
