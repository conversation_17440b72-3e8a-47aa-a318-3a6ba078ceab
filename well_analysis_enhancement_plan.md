# Plan: Well Analysis Sub-Options Enhancement

**Objective:** Introduce two new, distinct sub-options ("Plot Individual Wells Analysis" and "Grouping Well Analysis") under the "By well markers" mode. The UI for selecting these will be in `pages/select_area_page.py`, and the backend analysis logic in `pages/analyze_data_page.py` (and potentially utility modules) must precisely mirror the processing steps found in `backup/app_ref.py` (specifically lines 1494-1790).

---

## Phase 1: UI Modifications in `pages/select_area_page.py`

1.  **Modify "By well markers" section (currently around line 187):**
    *   The existing `plot_mode_wells` radio button (lines 232-237) will be **removed**.
    *   A new `st.radio` (or `st.selectbox`) will be added after the `st.multiselect` for `selected_well_marker_pairs` (line 223). This new UI element will allow users to choose between:
        *   "Plot Individual Wells Analysis"
        *   "Grouping Well Analysis"
    *   The user's choice will be stored in a new session state variable, for example, `st.session_state.well_analysis_sub_option`.
    *   The existing "Complete Step 3 and Continue" button (or its equivalent logic that transitions to the "Analyze Data" step) will proceed, now carrying the context of this new sub-option.

---

## Phase 2: Backend Logic Implementation in `pages/analyze_data_page.py`

1.  **Adapt `render()` function (currently around line 605 for "By well markers" mode):**
    *   The function will retrieve the value of `st.session_state.well_analysis_sub_option`.
    *   The logic triggered by the "Calculate Descriptors" button will be conditional based on this sub-option.

2.  **Implement Logic for "Plot Individual Wells Analysis":**
    *   This path will be taken if `st.session_state.well_analysis_sub_option == "Plot Individual Wells Analysis"`.
    *   The system will iterate through each selected well-marker pair (from `st.session_state.loaded_trace_data`, which is populated in `pages/select_area_page.py`).
    *   For each well:
        *   Descriptor calculation will be performed, mirroring the logic in `backup/app_ref.py` (lines 1530-1583). This involves using the appropriate spectral parameters and GPU functions.
        *   Statistics will be calculated *specifically for that individual well's descriptors*, again mirroring `backup/app_ref.py` (lines 1585-1779).
    *   The results (descriptors and statistics for each well) will be stored, perhaps in a list of dictionaries under a session state variable like `st.session_state.individual_well_analysis_results`.
    *   `st.session_state.analysis_complete` will be set to `True`.
    *   The "View Results" button will navigate to a results page designed to display these individual analyses.

3.  **Implement Logic for "Grouping Well Analysis":**
    *   This path will be taken if `st.session_state.well_analysis_sub_option == "Grouping Well Analysis"`.
    *   This will largely adapt the existing processing flow in `pages/analyze_data_page.py` (currently lines 633-1000+), which processes all loaded traces together and calculates aggregate statistics.
    *   Crucially, this existing logic for descriptor calculation (lines 646-751) and statistics calculation (lines 773 onwards) must be carefully reviewed and modified to *precisely mirror* the comprehensive approach in `backup/app_ref.py` (lines 1527-1779), especially regarding parameter usage, GPU function calls, and the full range of statistics calculated.
    *   The results will be stored in `st.session_state.calculated_descriptors` (containing descriptors for all traces) and `st.session_state.descriptor_statistics` (containing the aggregate statistics).
    *   `st.session_state.analysis_complete` will be set to `True`.
    *   The "View Results" button will navigate to a results page suitable for this grouped/comparative view.

---

## Phase 3: Ensure Helper Functions and Data Structures Align

1.  **Review Utility Modules (e.g., `utils/data_utils.py`, `utils/processing.py`, `utils/gpu_utils.py`):**
    *   Verify that all helper functions (e.g., `load_trace_sample`, `get_well_marker_pairs`, `calculate_woss`, GPU descriptor functions) are consistent with their usage and behavior as implied or defined in `backup/app_ref.py`.
    *   Ensure that spectral parameters and descriptor settings (e.g., `descriptor_settings`, `gpu_descriptor_settings` from `backup/app_ref.py` lines 1531-1541 and 1561-1564) are correctly sourced from `st.session_state.plot_settings` and consistently applied in both new analysis paths.

2.  **Session State Management:**
    *   Maintain clear and consistent use of session state variables, including the new `st.session_state.well_analysis_sub_option` and any new structures for storing individual well results.

---

## Phase 4: Verification and Testing

1.  Thoroughly test both "Plot Individual Wells Analysis" and "Grouping Well Analysis" sub-options.
2.  Where feasible, compare numerical outputs (descriptors, statistics) against those from a manual execution or a known-good version based on `backup/app_ref.py` to confirm accuracy and faithful replication.

---

## Visual Plan

**UI Flow in `pages/select_area_page.py` (Well Markers Mode):**
```mermaid
graph TD
    A[Start Step 3: Select Analysis Mode] --> B{Mode Selection};
    B -- "By well markers" --> C[Display Well Marker Selection UI];
    C --> D[User selects Well-Marker Pair(s) via st.multiselect];
    D --> E[User selects Analysis Sub-Option via st.radio/st.selectbox: <br> "Plot Individual Wells Analysis" OR <br> "Grouping Well Analysis"];
    E -- "Plot Individual Wells Analysis" --> F[Store 'individual' in st.session_state.well_analysis_sub_option];
    E -- "Grouping Well Analysis" --> G[Store 'grouping' in st.session_state.well_analysis_sub_option];
    F --> H[Button: "Complete Step 3 & Continue"];
    G --> H;
    H --> I[Transition to Analyze Data Page <br> (carrying 'well_analysis_sub_option')];
    B -- "Other Modes..." --> J[Existing UI for other modes];
    J --> H;
```

**Logic Flow in `pages/analyze_data_page.py` (When "By well markers" mode is active):**
```mermaid
graph TD
    K[Start Analyze Data Page - "By well markers" mode] --> L{Read st.session_state.well_analysis_sub_option};
    L -- "'Plot Individual Wells Analysis'" --> M["For each selected well-marker pair: <br> 1. Load Trace Data <br> 2. Calculate Descriptors (mirror app_ref.py) <br> 3. Calculate Statistics for THIS well (mirror app_ref.py)"];
    M --> N[Store individual results (list of per-well descriptors & stats)];
    N --> P[Set st.session_state.analysis_complete = true];
    P --> Q[Button: "View Results" (navigates to individual results view)];
    L -- "'Grouping Well Analysis'" --> R["1. Load Trace Data for ALL selected pairs <br> 2. Calculate Descriptors for ALL traces (mirror app_ref.py) <br> 3. Calculate Aggregate Statistics for ALL traces (mirror app_ref.py)"];
    R --> S[Store grouped results (all descriptors & aggregate stats)];
    S --> P;
    Q --> T[Navigate to appropriate Results Page];